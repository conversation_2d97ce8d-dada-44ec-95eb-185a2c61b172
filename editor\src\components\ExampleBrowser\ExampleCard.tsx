/**
 * 示例项目卡片组件
 * 用于在示例项目浏览器中显示示例项目的预览和基本信息
 */
import React from 'react';
import { Card, Tag, Tooltip } from 'antd';
import { EyeOutlined, ImportOutlined, StarOutlined, StarFilled } from '@ant-design/icons';
import { useTranslation } from 'react-i18next';
import { Example } from '../../types/example';
import { getCategoryColor, getTagColor } from '../../utils/exampleUtils';
import './ExampleCard.less';

interface ExampleCardProps {
  example: Example;
  onClick: () => void;
  onFavoriteToggle?: (favorited: boolean) => void;
}

/**
 * 示例项目卡片组件
 */
const ExampleCard: React.FC<ExampleCardProps> = ({ example, onClick, onFavoriteToggle }) => {
  const { t } = useTranslation();

  /**
   * 格式化日期
   * @param dateString 日期字符串
   * @returns 格式化后的日期字符串
   */
  const formatDate = (dateString: string): string => {
    const date = new Date(dateString);
    return date.toLocaleDateString();
  };

  /**
   * 处理收藏点击
   * @param e 事件对象
   */
  const handleFavoriteClick = (e: React.MouseEvent) => {
    e.stopPropagation();
    if (onFavoriteToggle) {
      onFavoriteToggle(!example.favorited);
    }
  };

  /**
   * 获取难度标签
   * @param difficulty 难度级别
   * @returns 难度标签元素
   */
  const getDifficultyTag = (difficulty: string): JSX.Element => {
    let color = '';
    let text = '';

    switch (difficulty) {
      case 'beginner':
        color = 'green';
        text = t('exampleBrowser.difficultyBeginner');
        break;
      case 'intermediate':
        color = 'blue';
        text = t('exampleBrowser.difficultyIntermediate');
        break;
      case 'advanced':
        color = 'orange';
        text = t('exampleBrowser.difficultyAdvanced');
        break;
      case 'expert':
        color = 'red';
        text = t('exampleBrowser.difficultyExpert');
        break;
      default:
        color = 'default';
        text = difficulty;
    }

    return (
      <Tag color={color} className="difficulty-tag">
        {text}
      </Tag>
    );
  };

  return (
    <Card
      hoverable
      className="example-card"
      cover={
        <div className="example-image-container">
          <img
            alt={example.title}
            src={example.previewImage}
            className="example-image"
          />
          <div className="example-category">
            <Tag color={getCategoryColor(example.category)}>
              {t(`exampleBrowser.category.${example.category}`)}
            </Tag>
          </div>
          {example.difficulty && (
            <div className="example-difficulty">
              {getDifficultyTag(example.difficulty)}
            </div>
          )}
        </div>
      }
      onClick={onClick}
    >
      <div className="example-card-content">
        <h3 className="example-title">{example.title}</h3>
        <p className="example-description">{example.description}</p>
        <div className="example-meta">
          <div className="example-tags">
            {example.tags.slice(0, 3).map((tag, index) => (
              <Tag key={index} color={getTagColor(tag)}>
                {tag}
              </Tag>
            ))}
            {example.tags.length > 3 && (
              <Tooltip title={example.tags.slice(3).join(', ')}>
                <Tag>+{example.tags.length - 3}</Tag>
              </Tooltip>
            )}
          </div>
          <div className="example-info">
            <Tooltip title={t('exampleBrowser.popularity')}>
              <span className="example-popularity">
                <StarOutlined /> {example.popularity}
              </span>
            </Tooltip>
            <Tooltip title={t('exampleBrowser.createdAt')}>
              <span className="example-date">
                {formatDate(example.createdAt)}
              </span>
            </Tooltip>
          </div>
        </div>
        <div className="example-actions">
          <Tooltip title={example.favorited ? t('exampleBrowser.unfavorite') : t('exampleBrowser.favorite')}>
            {example.favorited ? (
              <StarFilled className="example-action-icon favorited" onClick={handleFavoriteClick} />
            ) : (
              <StarOutlined className="example-action-icon" onClick={handleFavoriteClick} />
            )}
          </Tooltip>
          <Tooltip title={t('exampleBrowser.view')}>
            <EyeOutlined className="example-action-icon" />
          </Tooltip>
          <Tooltip title={t('exampleBrowser.import')}>
            <ImportOutlined className="example-action-icon" />
          </Tooltip>
        </div>
      </div>
    </Card>
  );
};

export default ExampleCard;
