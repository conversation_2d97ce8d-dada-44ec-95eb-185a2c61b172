/**
 * 示例项目过滤器组件
 * 用于过滤和搜索示例项目
 */
import React, { useState, useEffect } from 'react';
import { Select, Tag, Button, Radio } from 'antd';
import { FilterOutlined, ClearOutlined } from '@ant-design/icons';
import { useTranslation } from 'react-i18next';
import { fetchExampleTags } from '../../services/exampleService';
import { ExampleCategory } from '../../types/example';
import { getCategoryColor, getTagColor } from '../../utils/exampleUtils';
import './ExampleFilter.less';

const { Option } = Select;
const { Group, Button: RadioButton } = Radio;

interface ExampleFilterProps {
  selectedCategory: string;
  selectedTags: string[];
  onCategoryChange: (category: string) => void;
  onTagsChange: (tags: string[]) => void;
}

/**
 * 示例项目过滤器组件
 */
const ExampleFilter: React.FC<ExampleFilterProps> = ({
  selectedCategory,
  selectedTags,
  onCategoryChange,
  onTagsChange}) => {
  const { t } = useTranslation();
  const [availableTags, setAvailableTags] = useState<string[]>([]);
  const [expanded, setExpanded] = useState<boolean>(false);

  /**
   * 加载可用标签
   */
  useEffect(() => {
    const loadTags = async () => {
      try {
        const tags = await fetchExampleTags();
        setAvailableTags(tags);
      } catch (error) {
        console.error('加载标签失败:', error);
      }
    };

    loadTags();
  }, []);

  /**
   * 处理类别变更
   * @param e 事件对象
   */
  const handleCategoryChange = (e: any) => {
    onCategoryChange(e.target.value);
  };

  /**
   * 处理标签变更
   * @param tags 选中的标签
   */
  const handleTagsChange = (tags: string[]) => {
    onTagsChange(tags);
  };

  /**
   * 处理清除过滤器
   */
  const handleClearFilters = () => {
    onCategoryChange('all');
    onTagsChange([]);
  };

  /**
   * 切换展开状态
   */
  const toggleExpanded = () => {
    setExpanded(!expanded);
  };

  /**
   * 渲染类别选项
   */
  const renderCategoryOptions = () => {
    const categories: { value: string; label: string }[] = [
      { value: 'all', label: t('exampleBrowser.category.all') },
      { value: 'basic', label: t('exampleBrowser.category.basic') },
      { value: 'material', label: t('exampleBrowser.category.material') },
      { value: 'animation', label: t('exampleBrowser.category.animation') },
      { value: 'physics', label: t('exampleBrowser.category.physics') },
      { value: 'visualscript', label: t('exampleBrowser.category.visualscript') },
      { value: 'performance', label: t('exampleBrowser.category.performance') },
      { value: 'collaboration', label: t('exampleBrowser.category.collaboration') },
      { value: 'tutorial', label: t('exampleBrowser.category.tutorial') },
    ];

    return categories.map(category => (
      <RadioButton 
        key={category.value} 
        value={category.value}
        className={`category-button ${selectedCategory === category.value ? 'selected' : ''}`}
      >
        {category.label}
      </RadioButton>
    ));
  };

  /**
   * 渲染标签选项
   */
  const renderTagOptions = () => {
    return availableTags.map(tag => (
      <Option key={tag} value={tag}>
        <Tag color={getTagColor(tag)}>{tag}</Tag>
      </Option>
    ));
  };

  return (
    <div className={`example-filter ${expanded ? 'expanded' : ''}`}>
      <div className="filter-header">
        <Button
          icon={<FilterOutlined />}
          onClick={toggleExpanded}
          type={expanded ? 'primary' : 'default'}
          className="expand-button"
        >
          {expanded ? t('exampleBrowser.hideFilters') : t('exampleBrowser.showFilters')}
        </Button>
        {(selectedCategory !== 'all' || selectedTags.length > 0) && (
          <Button
            icon={<ClearOutlined />}
            onClick={handleClearFilters}
            className="clear-button"
          >
            {t('exampleBrowser.clearFilters')}
          </Button>
        )}
      </div>
      {expanded && (
        <div className="filter-content">
          <div className="filter-section">
            <h4>{t('exampleBrowser.categories')}</h4>
            <Group 
              value={selectedCategory} 
              onChange={handleCategoryChange}
              className="category-group"
            >
              {renderCategoryOptions()}
            </Group>
          </div>
          <div className="filter-section">
            <h4>{t('exampleBrowser.tags')}</h4>
            <Select
              mode="multiple"
              placeholder={t('exampleBrowser.selectTags')}
              value={selectedTags}
              onChange={handleTagsChange}
              style={{ width: '100%' }}
              maxTagCount={5}
              maxTagTextLength={10}
            >
              {renderTagOptions()}
            </Select>
          </div>
          <div className="filter-section">
            <h4>{t('exampleBrowser.difficulty')}</h4>
            <Group className="difficulty-group">
              <RadioButton value="all">{t('exampleBrowser.difficultyAll')}</RadioButton>
              <RadioButton value="beginner">{t('exampleBrowser.difficultyBeginner')}</RadioButton>
              <RadioButton value="intermediate">{t('exampleBrowser.difficultyIntermediate')}</RadioButton>
              <RadioButton value="advanced">{t('exampleBrowser.difficultyAdvanced')}</RadioButton>
              <RadioButton value="expert">{t('exampleBrowser.difficultyExpert')}</RadioButton>
            </Group>
          </div>
        </div>
      )}
    </div>
  );
};

export default ExampleFilter;
