/**
 * 示例项目浏览器组件
 * 用于浏览、搜索和加载示例项目
 */
import React, { useState, useEffect } from 'react';
import { Layout, Select, Button, Row, Col, Spin, Empty, message, Input } from 'antd';
import { SearchOutlined, FilterOutlined, ReloadOutlined } from '@ant-design/icons';
import { useTranslation } from 'react-i18next';
import ExampleCard from './ExampleCard';
import ExampleFilter from './ExampleFilter';
import ExampleDetail from './ExampleDetail';
import ImportDialog from './ImportDialog';
import { fetchExamples } from '../../services/exampleService';
import { Example, ExampleCategory } from '../../types/example';
import './ExampleBrowser.less';

const { Header, Content } = Layout;
const { Search } = Input;
const { Option } = Select;
// 移除重复导入

/**
 * 示例项目浏览器组件
 */
const ExampleBrowser: React.FC = () => {
  const { t } = useTranslation();
  const [examples, setExamples] = useState<Example[]>([]);
  const [filteredExamples, setFilteredExamples] = useState<Example[]>([]);
  const [selectedCategory, setSelectedCategory] = useState<string>('all');
  const [selectedTags, setSelectedTags] = useState<string[]>([]);
  const [searchText, setSearchText] = useState<string>('');
  const [selectedExample, setSelectedExample] = useState<Example | null>(null);
  const [loading, setLoading] = useState<boolean>(true);
  const [importDialogVisible, setImportDialogVisible] = useState<boolean>(false);
  const [sortBy, setSortBy] = useState<string>('newest');

  /**
   * 加载示例项目数据
   */
  useEffect(() => {
    loadExamples();
  }, []);

  /**
   * 加载示例项目
   */
  const loadExamples = async () => {
    setLoading(true);
    try {
      const data = await fetchExamples();
      setExamples(data);
      setFilteredExamples(data);
    } catch (error) {
      console.error('加载示例项目失败:', error);
      message.error(t('exampleBrowser.loadError'));
    }
    setLoading(false);
  };

  /**
   * 处理类别过滤
   * @param category 类别
   */
  const handleCategoryChange = (category: string) => {
    setSelectedCategory(category);
    filterExamples(category, selectedTags, searchText);
  };

  /**
   * 处理标签过滤
   * @param tags 标签列表
   */
  const handleTagsChange = (tags: string[]) => {
    setSelectedTags(tags);
    filterExamples(selectedCategory, tags, searchText);
  };

  /**
   * 处理搜索
   * @param text 搜索文本
   */
  const handleSearch = (text: string) => {
    setSearchText(text);
    filterExamples(selectedCategory, selectedTags, text);
  };

  /**
   * 处理排序
   * @param value 排序方式
   */
  const handleSortChange = (value: string) => {
    setSortBy(value);
    sortExamples(value, filteredExamples);
  };

  /**
   * 过滤示例项目
   * @param category 类别
   * @param tags 标签列表
   * @param text 搜索文本
   */
  const filterExamples = (category: string, tags: string[], text: string) => {
    let filtered = [...examples];

    // 按类别过滤
    if (category !== 'all') {
      filtered = filtered.filter(example => example.category === category);
    }

    // 按标签过滤
    if (tags.length > 0) {
      filtered = filtered.filter(example => 
        tags.every(tag => example.tags.includes(tag))
      );
    }

    // 按文本搜索
    if (text) {
      const lowerText = text.toLowerCase();
      filtered = filtered.filter(example => 
        example.title.toLowerCase().includes(lowerText) || 
        example.description.toLowerCase().includes(lowerText)
      );
    }

    // 排序
    sortExamples(sortBy, filtered);
  };

  /**
   * 排序示例项目
   * @param sortType 排序类型
   * @param exampleList 示例项目列表
   */
  const sortExamples = (sortType: string, exampleList: Example[]) => {
    let sorted = [...exampleList];

    switch (sortType) {
      case 'newest':
        sorted.sort((a, b) => new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime());
        break;
      case 'oldest':
        sorted.sort((a, b) => new Date(a.createdAt).getTime() - new Date(b.createdAt).getTime());
        break;
      case 'name':
        sorted.sort((a, b) => a.title.localeCompare(b.title));
        break;
      case 'popular':
        sorted.sort((a, b) => b.popularity - a.popularity);
        break;
      default:
        break;
    }

    setFilteredExamples(sorted);
  };

  /**
   * 处理示例项目选择
   * @param example 示例项目
   */
  const handleExampleSelect = (example: Example) => {
    setSelectedExample(example);
  };

  /**
   * 处理导入按钮点击
   */
  const handleImportClick = () => {
    if (selectedExample) {
      setImportDialogVisible(true);
    }
  };

  /**
   * 处理导入确认
   * @param projectName 项目名称
   * @param location 保存位置
   */
  const handleImportConfirm = (projectName: string, location: string) => {
    if (selectedExample) {
      // 导入示例项目
      message.success(t('exampleBrowser.importSuccess', { name: projectName }));
      setImportDialogVisible(false);
    }
  };

  /**
   * 处理刷新按钮点击
   */
  const handleRefresh = () => {
    loadExamples();
  };

  return (
    <Layout className="example-browser">
      <Header className="example-browser-header">
        <div className="header-title">
          <h2>{t('exampleBrowser.title')}</h2>
          <Button
            icon={<ReloadOutlined />}
            onClick={handleRefresh}
            title={String(t('exampleBrowser.refresh'))}
          />
        </div>
        <div className="header-search">
          <Search
            placeholder={t('exampleBrowser.searchPlaceholder')}
            onSearch={handleSearch}
            onChange={(e) => setSearchText(e.target.value)}
            value={searchText}
            allowClear
            style={{ width: 300 }}
            prefix={<SearchOutlined />}
          />
        </div>
        <div className="header-filter">
          <ExampleFilter
            selectedCategory={selectedCategory}
            selectedTags={selectedTags}
            onCategoryChange={handleCategoryChange}
            onTagsChange={handleTagsChange}
          />
          <Select 
            defaultValue="newest" 
            style={{ width: 120 }} 
            onChange={handleSortChange}
            value={sortBy}
          >
            <Option value="newest">{t('exampleBrowser.sortNewest')}</Option>
            <Option value="oldest">{t('exampleBrowser.sortOldest')}</Option>
            <Option value="name">{t('exampleBrowser.sortName')}</Option>
            <Option value="popular">{t('exampleBrowser.sortPopular')}</Option>
          </Select>
        </div>
      </Header>
      <Content className="example-browser-content">
        {loading ? (
          <div className="loading-container">
            <Spin size="large" />
            <p>{t('exampleBrowser.loading')}</p>
          </div>
        ) : selectedExample ? (
          <ExampleDetail
            example={selectedExample}
            onBack={() => setSelectedExample(null)}
            onImport={handleImportClick}
          />
        ) : filteredExamples.length > 0 ? (
          <Row gutter={[16, 16]} className="example-grid">
            {filteredExamples.map(example => (
              <Col key={example.id} xs={24} sm={12} md={8} lg={6}>
                <ExampleCard
                  example={example}
                  onClick={() => handleExampleSelect(example)}
                />
              </Col>
            ))}
          </Row>
        ) : (
          <Empty
            description={t('exampleBrowser.noExamples')}
            image={Empty.PRESENTED_IMAGE_SIMPLE}
          />
        )}
      </Content>
      <ImportDialog
        visible={importDialogVisible}
        example={selectedExample}
        onCancel={() => setImportDialogVisible(false)}
        onConfirm={handleImportConfirm}
      />
    </Layout>
  );
};

export default ExampleBrowser;
